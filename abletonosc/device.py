from typing import <PERSON>ple, Any
from .handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import <PERSON><PERSON>, Any
from time import time
from .device_mode import DeviceMode  # au lieu de from . import DeviceMode
from .device_learn import DeviceLearn  # Ajouter cette import

class DeviceHandler(AbletonOSCHandler):
    def __init__(self, manager):
        super().__init__(manager)
        self.class_identifier = "device"
        self.last_parameter_updates = {}
        self.parameter_throttle_time = 0.05
        
        # Initialiser device_mode et device_learn après l'initialisation de base
        self.device_mode = DeviceMode(self)
        self.device_learn = DeviceLearn(self)
        
        self.logger.warning(f"DeviceHandler instance created with id: {id(self)}")

    def init_api(self):
        def create_device_callback(func, *args, include_ids: bool = False):
            def device_callback(params: Tuple[Any]):
                track_index, device_index = int(params[0]), int(params[1])
                
                visible_tracks = [track for track in self.song.tracks if track.is_visible]
                total_tracks = len(visible_tracks) + len(self.song.return_tracks) + 1
                
                if track_index < len(visible_tracks):
                    track = visible_tracks[track_index]
                elif track_index < len(visible_tracks) + len(self.song.return_tracks):
                    track = self.song.return_tracks[track_index - len(visible_tracks)]
                else:
                    track = self.song.master_track
                
                visible_devices = self.manager.get_visible_devices(track)
                
                if device_index < len(visible_devices):
                    device = visible_devices[device_index]
                    if include_ids:
                        rv = func(device, *args, params[0:])
                    else:
                        rv = func(device, *args, params[2:])

                    if rv is not None:
                        return (track_index, device_index, *rv)
                else:
                    self.logger.warning(f"Device index {device_index} out of range on track {track_index}")

            return device_callback

        # Configuration des handlers de base
        methods = []
        properties_r = [
            "class_name",
            "name",
            "type",
            "can_have_chains",
            "is_collapsed"
        ]
        properties_rw = []

        # Configuration des handlers de base pour les propriétés
        for method in methods:
            self.osc_server.add_handler("/live/device/%s" % method,
                                        create_device_callback(self._call_method, method))

        for prop in properties_r + properties_rw:
            self.osc_server.add_handler("/live/device/get/%s" % prop,
                                        create_device_callback(self._get_property, prop))
            self.osc_server.add_handler("/live/device/start_listen/%s" % prop,
                                        create_device_callback(self._start_listen, prop))
            self.osc_server.add_handler("/live/device/stop_listen/%s" % prop,
                                        create_device_callback(self._stop_listen, prop))
        for prop in properties_rw:
            self.osc_server.add_handler("/live/device/set/%s" % prop,
                                        create_device_callback(self._set_property, prop))

        # Configuration du handler pour le toggle collapsed
        def device_toggle_collapsed(device, params: Tuple[Any]):
            device.view.is_collapsed = not device.view.is_collapsed
            track_id = params[0] if params else 0
            device_id = params[1] if len(params) > 1 else 0
            self.osc_server.send("/live/device/get/is_collapsed", 
                                (track_id, device_id, int(device.view.is_collapsed)))

        self.osc_server.add_handler("/live/device/set/is_collapsed", 
                               create_device_callback(device_toggle_collapsed))

        # Configuration des handlers pour le mode device et learn
        self.osc_server.add_handler("/live/learn/setup_device", 
            lambda x: self.device_learn.setup_learn_listeners())
        self.osc_server.add_handler("/live/learn/stop_device", 
            lambda x: self.device_learn.stop_learn_listeners())

        # Ces handlers doivent être ajoutés directement, sans passer par create_device_callback
        self.osc_server.add_handler("/live/deviceMode/setup", 
            lambda x: self.device_mode.start_selected_device_listeners())
        self.osc_server.add_handler("/live/deviceMode/stop", 
            lambda x: self.device_mode.stop_selected_device_listeners())

        # Configuration du handler pour le device lock
        self.osc_server.add_handler("/live/device_lock", 
            lambda x: self.device_mode.deviceLock_handler(x))
        
        # Ajout des handlers pour isActive
        self.osc_server.add_handler("/live/device/set/isActiveToggle", 
            lambda params: self.device_mode.toggle_device_active(params))
        
        # Ajout du nouveau handler pour toggle par chemin
        self.osc_server.add_handler("/live/device/set/isActiveTogByPath", 
            lambda params: self.device_mode.toggle_device_active_by_path(params))
        
        self.osc_server.add_handler("/live/device/learning/parameter/value", 
            lambda params: self.device_learn.handle_device_learning(params))
        
        self.osc_server.add_handler("/live/chain/learning/volume", 
            lambda params: self.device_learn.handle_chain_volume_learning(params))
        
        self.osc_server.add_handler("/live/chain/learning/panning", 
            lambda params: self.device_learn.handle_chain_panning_learning(params))

        # Handlers de navigation - ajoutés directement sans create_device_callback
        self.osc_server.add_handler("/live/select/chain", 
            lambda params: self.device_mode.select_rack_chain(params))
        self.osc_server.add_handler("/live/select/device", 
            lambda params: self.device_mode.select_chain_device(params))
        # Ajout du handler pour la sélection par note MIDI
        self.osc_server.add_handler("/live/select/chain_by_note", 
            lambda params: self.device_mode.select_chain_by_note(params))

        # Ajout du handler pour la suppression par chemin
        self.osc_server.add_handler("/live/device/delete_by_path", 
            lambda params: self.device_mode.delete_device_by_path(params))

        # Ajout du handler pour le déplacement de device par chemin
        self.osc_server.add_handler("/live/device/move_by_path", 
            lambda params: self.device_mode.move_device_by_path(params))

        # Ajout du handler pour la modification de paramètre par chemin
        self.osc_server.add_handler("/live/device/set/parameter/value", 
            lambda params: self.device_mode.set_device_parameter_by_path(params))

        # Ajout des handlers pour le contrôle des chaînes
        self.osc_server.add_handler("/live/chain/set/volume", 
            lambda params: self.device_mode.set_chain_volume_by_path(params))
        
        self.osc_server.add_handler("/live/chain/set/pan", 
            lambda params: self.device_mode.set_chain_pan_by_path(params))
        
        self.osc_server.add_handler("/live/chain/set/togglemute", 
            lambda params: self.device_mode.toggle_chain_mute_by_path(params))
        
        self.osc_server.add_handler("/live/chain/set/togglesolo", 
            lambda params: self.device_mode.toggle_chain_solo_by_path(params))

    def _get_property(self, obj, prop_name, params=()):
        """Surcharge pour gérer les propriétés spéciales des devices"""
        if prop_name == "is_collapsed":
            return (obj.view.is_collapsed,)
        elif prop_name == "can_have_chains":
            return (obj.can_have_chains,)
        return super()._get_property(obj, prop_name, params)
