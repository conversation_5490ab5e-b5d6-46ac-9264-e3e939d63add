# Intégration ButtonMode avec envoi de messages OSC

## Problème résolu

Le fi<PERSON> `Communication/buttonMode.go` définissait des actions communes pour les boutons (Play, Stop, Record) mais ne savait pas comment envoyer des messages OSC. Cette documentation explique la solution implémentée.

## Solution

### 1. Modification de `CommonButtonActions`

Les actions communes reçoivent maintenant une référence au `BaseMode` pour pouvoir envoyer des messages OSC :

```go
type CommonButtonActions struct {
    Play   func(baseMode *BaseMode) bool
    Stop   func(baseMode *BaseMode) bool
    Record func(baseMode *BaseMode) bool
}
```

### 2. Fonction `SetupCommonButtonHandlers` mise à jour

```go
func SetupCommonButtonHandlers(bm *ButtonManager, actions *CommonButtonActions, baseMode *BaseMode) {
    if actions.Play != nil {
        bm.RegisterCommonHandler(10, func(buttonIndex int) bool {
            return actions.Play(baseMode)
        })
    }
    // ... autres boutons
}
```

### 3. Actions par défaut avec envoi OSC

```go
func DefaultCommonButtonActions() *CommonButtonActions {
    return &CommonButtonActions{
        Play: func(baseMode *BaseMode) bool {
            if baseMode != nil {
                err := baseMode.Send("/live/play")
                return err == nil
            }
            return false
        },
        // ... autres actions
    }
}
```

## Utilisation dans un mode

### Étape 1: Implémenter l'interface `ButtonMode`

```go
type MonMode struct {
    *communication.BaseMode
    buttonManager *communication.ButtonManager
}

func (m *MonMode) GetButtonManager() *ButtonManager {
    return m.buttonManager
}

func (m *MonMode) SetupCommonButtons() {
    // Option 1: Actions par défaut
    defaultActions := DefaultCommonButtonActions()
    SetupCommonButtonHandlers(m.buttonManager, defaultActions, m.BaseMode)

    // Option 2: Actions personnalisées
    // customActions := m.createCustomActions()
    // SetupCommonButtonHandlers(m.buttonManager, customActions, m.BaseMode)
}

func (m *MonMode) SetupModeButtons() {
    // Boutons spécifiques au mode
    m.buttonManager.RegisterModeHandler(0, func(buttonIndex int) bool {
        err := m.BaseMode.Send("/live/mode/specific/action")
        return err == nil
    })
}
```

### Étape 2: Initialiser les boutons

```go
func (m *MonMode) Initialize(service OscService) {
    m.BaseMode.Initialize(service)

    // Configurer les boutons
    m.SetupCommonButtons()
    m.SetupModeButtons()
}
```

### Étape 3: Traiter les événements de boutons

```go
func (m *MonMode) handleButtonEvent(event *ButtonEvent) {
    if event == nil || event.State != 1 {
        return
    }

    // Utiliser ButtonManager pour traiter l'événement
    handled := m.buttonManager.HandleButton(event.Index)

    if !handled {
        // Fallback si nécessaire
    }
}
```

## Messages OSC envoyés

### Boutons communs (par défaut)

- **B10 (Play)**: `/live/play`
- **B11 (Stop)**: `/live/stop`
- **B12 (Record)**: `/live/record`

### Actions personnalisées

Vous pouvez créer des actions personnalisées qui envoient des messages différents :

```go
func (m *MonMode) createCustomActions() *CommonButtonActions {
    return &CommonButtonActions{
        Play: func(baseMode *BaseMode) bool {
            // Envoyer plusieurs messages
            baseMode.Send("/live/song/play")
            baseMode.Send("/live/transport/start")
            return true
        },
        Stop: func(baseMode *BaseMode) bool {
            // Message avec paramètres
            baseMode.Send("/live/song/stop", "immediate")
            return true
        },
        Record: func(baseMode *BaseMode) bool {
            // Message complexe
            baseMode.Send("/live/song/record", map[string]interface{}{
                "mode": "overdub",
                "quantize": true,
            })
            return true
        },
    }
}
```

## Avantages de cette approche

1. **Réutilisabilité**: Les actions communes peuvent être partagées entre modes
2. **Flexibilité**: Chaque mode peut personnaliser les actions communes
3. **Séparation des responsabilités**: ButtonManager gère la logique, BaseMode gère l'OSC
4. **Facilité de test**: Les actions peuvent être testées indépendamment
5. **Maintenance**: Centralisation de la logique des boutons communs

## Exemple d'intégration dans LiveVolumeMode

Voir `Communication/buttonMode_integration_example.go` pour un exemple complet d'intégration dans un mode existant.

## Fichiers modifiés/créés

- `Communication/buttonMode.go` - Interface et actions mises à jour
- `Communication/buttonMode_example.go` - Exemple d'utilisation complète
- `Communication/buttonMode_integration_example.go` - Exemple d'intégration dans un mode existant
- `Documentation/ButtonMode_OSC_Integration.md` - Cette documentation
