package livedevicemode

import (
	"fmt"
	"log"
	communication "oscbridge/Communication"
	live "oscbridge/Live"
	"sync"
)

// LiveDeviceMode gère le mode device de Live
type LiveDeviceMode struct {
	*communication.BaseMode
	trackManager     *live.LiveTrackManager
	commManager      *communication.CommunicationManager
	state            *LiveDeviceModeState
	parameterManager *DeviceParameterManager
	propertyManager  *DevicePropertyManager
	isActive         bool

	// Champs pour la gestion des handlers
	backgroundHandlers map[string]func([]interface{})
	activeHandlers     map[string]func([]interface{})
	hardwareHandler    func(communication.HardwareEvent)

	// Mutex pour protéger les accès concurrents
	stateMutex sync.Mutex
}

// NewLiveDeviceMode crée une nouvelle instance du mode device
func NewLiveDeviceMode(trackManager *live.LiveTrackManager, commManager *communication.CommunicationManager) *LiveDeviceMode {
	mode := &LiveDeviceMode{
		BaseMode:     communication.NewBaseMode(),
		trackManager: trackManager,
		commManager:  commManager,
		state: &LiveDeviceModeState{
			IsActive:    false,
			IsLocked:    false,
			SubMode:     SubmodeDevice,
			CurrentPage: 1,
			Path:        make([]int, 0),
			IsDrumRack:  false,
		},
		backgroundHandlers: make(map[string]func([]interface{})),
		activeHandlers:     make(map[string]func([]interface{})),
	}

	// Les managers seront initialisés dans Initialize()
	return mode
}

// Initialize initialise le mode device
func (m *LiveDeviceMode) Initialize(service communication.OscService) {
	log.Println("Initialisation du mode device...")

	// Initialiser le mode de base
	m.BaseMode.Initialize(service)

	// Stocker BaseMode dans l'état pour les gestionnaires
	m.state.BaseMode = m.BaseMode

	// Initialiser les managers
	m.parameterManager = NewDeviceParameterManager(m.commManager, m.BaseMode, m.state)
	m.parameterManager.SetParentMode(m)
	m.propertyManager = NewDevicePropertyManager(m.commManager, m.trackManager, m.BaseMode, m.state)
	m.propertyManager.SetParentMode(m)

	// Enregistrer les handlers OSC background
	m.registerOSCHandlers()

	// Enregistrer les écouteurs d'événements background
	m.registerEventHandlers()

	// Envoyer le message de déverrouillage au format attendu par Live
	// Note: Le JS envoie "0" comme chaîne, pas comme entier
	m.BaseMode.Send("/live/device_lock", "0")

	log.Println("Mode device initialisé avec succès.")
}

// registerEventHandlers enregistre les gestionnaires d'événements
func (m *LiveDeviceMode) registerEventHandlers() {
	// Enregistrer l'écouteur pour les changements de device sélectionné
	m.trackManager.On("selectedTrackDeviceUpdate", func(args []interface{}) {
		if len(args) >= 4 {
			var selectedTrack, selectedDevice, devicesCount, parametersCount int

			// Extraire les valeurs des arguments
			if st, ok := args[0].(*int); ok && st != nil {
				selectedTrack = *st
			}
			if sd, ok := args[1].(*int); ok && sd != nil {
				selectedDevice = *sd
			}
			if dc, ok := args[2].(int); ok {
				devicesCount = dc
			}
			if pc, ok := args[3].(int); ok {
				parametersCount = pc
			}

			m.onSelectedTrackDeviceChange(selectedTrack, selectedDevice, devicesCount, parametersCount)
		}
	})

	log.Println("Événements enregistrés")
}

// Activate active le mode device et démarre les listeners actifs
func (m *LiveDeviceMode) Activate() {
	log.Println("\n=== Activating DeviceMode ===")
	m.BaseMode.Activate()
	m.isActive = true

	// Démarrer uniquement les listeners actifs
	log.Println("Démarrage des listeners actifs...")

	// Configurer le handler hardware
	m.hardwareHandler = m.HandleHardwareEvent
	m.commManager.AddHardwareEventHandler(m.hardwareHandler)

	// Activer l'affichage du mode device
	m.commManager.SendMessage("mo,2", m.isActive)

	// Si aucun device n'est présent et que le mode n'est pas verrouillé
	if m.trackManager.GetDevicesCount() == 0 && !m.state.IsLocked {
		m.commManager.SendMessage("di,0,nul,0,nul,0,nul,0,nul,0,nul,0,nul,0,nul,0,nul", m.isActive)
		m.commManager.SendMessage("dd,0,-1", m.isActive)
	}

	log.Println("=== DeviceMode Activation Complete ===")
}

// Deactivate désactive le mode device
func (m *LiveDeviceMode) Deactivate() {
	log.Println("\n=== Deactivating DeviceMode ===")
	m.BaseMode.Deactivate()
	m.isActive = false

	// Supprimer uniquement les handlers actifs
	if m.hardwareHandler != nil {
		m.commManager.RemoveHardwareEventHandler(m.hardwareHandler)
		m.hardwareHandler = nil
	}

	log.Println("=== DeviceMode Deactivation Complete ===")
}

// CleanupForExit nettoie toutes les ressources avant la sortie de l'application
func (m *LiveDeviceMode) CleanupForExit() {
	log.Println("\n=== DeviceMode Complete Cleanup Start ===")

	// Désactiver d'abord le mode s'il est actif
	if m.isActive {
		m.Deactivate()
	}

	// Supprimer les handlers background
	for address := range m.backgroundHandlers {
		m.BaseMode.UnregisterHandler(address)
		delete(m.backgroundHandlers, address)
	}

	log.Println("=== DeviceMode Complete Cleanup Complete ===")
}

// IsActive retourne si le mode est actif
func (m *LiveDeviceMode) IsActive() bool {
	return m.isActive
}

// EnableLock active le verrouillage sur le device actuel
func (m *LiveDeviceMode) EnableLock() {
	m.stateMutex.Lock()
	defer m.stateMutex.Unlock()

	if m.state.IsLocked {
		return // Déjà verrouillé
	}

	// Ces fonctions retournent déjà des pointeurs
	device := m.trackManager.GetSelectedDevice()
	track := m.trackManager.GetSelectedTrack()

	if device == nil || track == nil {
		log.Println("Impossible de verrouiller : device ou track est nil")
		return
	}

	log.Printf("Activation du lock: device=%d, track=%d", *device, *track)

	m.state.IsLocked = true

	// Copier les valeurs pour éviter les modifications externes
	deviceVal := *device
	trackVal := *track

	devicePtr := new(int)
	trackPtr := new(int)
	*devicePtr = deviceVal
	*trackPtr = trackVal

	m.state.LockedDevice = devicePtr
	m.state.LockedTrack = trackPtr

	// Envoyer le message de verrouillage au format attendu par Live
	// Note: Le JS envoie simplement "1" comme chaîne, pas comme tableau
	m.BaseMode.Send("/live/device_lock", "1")
}

// DisableLock désactive le verrouillage
func (m *LiveDeviceMode) DisableLock() {
	m.stateMutex.Lock()
	defer m.stateMutex.Unlock()

	if !m.state.IsLocked {
		return // Déjà déverrouillé
	}

	log.Println("Désactivation du lock")

	m.state.IsLocked = false
	m.state.LockedDevice = nil
	m.state.LockedTrack = nil

	// Envoyer le message de déverrouillage au format attendu par le matériel
	m.commManager.SendMessage("ld00", m.isActive)

	// Envoyer le message de déverrouillage au format attendu par Live
	// Note: Le JS envoie "0" comme chaîne, pas comme entier
	m.BaseMode.Send("/live/device_lock", "0")

	// Rafraîchir l'affichage des paramètres
	m.parameterManager.HandleUnlock()
}

// onSelectedTrackDeviceChange gère le changement de device sélectionné
func (m *LiveDeviceMode) onSelectedTrackDeviceChange(selectedTrack, selectedDevice, devicesCount, parametersCount int) {
	m.stateMutex.Lock()
	defer m.stateMutex.Unlock()

	// Si le mode est verrouillé, ignorer les changements de device sélectionné
	if m.state.IsLocked {
		log.Printf("Mode verrouillé - Ignorant le changement de device sélectionné (track=%d, device=%d)", selectedTrack, selectedDevice)
		return
	}

	// Réinitialiser la page
	m.state.CurrentPage = 1

	// Mettre à jour l'affichage de la page
	totalPages := (parametersCount + SlotsPerPage - 1) / SlotsPerPage
	m.commManager.SendMessage(fmt.Sprintf("pd%d/%d", m.state.CurrentPage, totalPages), m.isActive)

	// Si aucun device n'est présent et que le mode n'est pas verrouillé
	if devicesCount == 0 && !m.state.IsLocked {
		m.commManager.SendMessage("di,0,nul,0,nul,0,nul,0,nul,0,nul,0,nul,0,nul,0,nul", m.isActive)
		m.commManager.SendMessage("dd,0,-1", m.isActive)
		m.commManager.SendMessage("pd,-1", m.isActive)
		m.commManager.SendMessage("ds,no device.", m.isActive)
	}
}

// Les méthodes suivantes seront implémentées dans d'autres fichiers :
// - registerOSCHandlers dans liveDevice_oscHandlers.go
// - HandleHardwareEvent et autres handlers hardware dans liveDevice_hardwareHandlers.go
// - registerEventHandlers et autres gestionnaires d'événements dans le fichier approprié
