package live

import (
	"fmt"
	"log"
	communication "oscbridge/Communication"
	"oscbridge/Live/types"
	"oscbridge/Live/utils"
	"sync"
	"time"
)

// LiveModeManager gère tous les modes et la communication OSC
type LiveModeManager struct {
	oscBridge             *communication.RawOscBridge
	trackManager          *LiveTrackManager
	modes                 map[string]communication.OscMode
	emitter               *communication.EventEmitter
	currentActiveModeName string
	mu                    sync.RWMutex
	volumeConverter       *utils.VolumeConverter
	volumeSendConverter   *utils.VolumeSendConverter
	learnData             *types.LearnData
	isInQuickView         bool
	previousModeName      string
}

// NewLiveModeManager crée une nouvelle instance du gestionnaire de modes
func NewLiveModeManager(oscBridge *communication.RawOscBridge) (*LiveModeManager, error) {
	if oscBridge == nil {
		return nil, fmt.Errorf("le bridge OSC fourni ne peut pas être nil")
	}

	manager := &LiveModeManager{
		oscBridge:             oscBridge,
		modes:                 make(map[string]communication.OscMode),
		emitter:               communication.NewEventEmitter(),
		currentActiveModeName: "",
		volumeConverter:       utils.NewVolumeConverter(),
		volumeSendConverter:   utils.NewVolumeSendConverter(),
		learnData:             nil,
		isInQuickView:         false,
		previousModeName:      "",
	}

	// Initialiser le gestionnaire de pistes
	trackManager := NewLiveTrackManager(manager)
	manager.trackManager = trackManager

	// Configurer et démarrer le track manager
	trackManager.Initialize(60 * time.Second)

	// Configurer le gestionnaire de messages
	manager.setupMessageHandler()

	// Configurer les écouteurs pour les demandes de QuickView
	manager.setupQuickViewListeners()

	return manager, nil
}

// setupMessageHandler configure le gestionnaire de messages OSC
func (m *LiveModeManager) setupMessageHandler() {
	if m.oscBridge == nil {
		println("ERREUR: Tentative de configurer les handlers OSC sans bridge valide.")
		return
	}
	m.oscBridge.On("*", func(args []interface{}) {
		if len(args) > 0 {
			if address, ok := args[0].(string); ok {
				realArgs := args[1:]
				m.emitter.Emit(address, realArgs)
			}
		}
	})

	// Ajouter un écouteur pour l'événement "modeChange"
	m.emitter.On("modeChange", func(args []interface{}) {
		if len(args) == 0 {
			log.Println("[LiveModeManager] Événement modeChange reçu sans argument de mode")
			return
		}

		targetMode, ok := args[0].(string)
		if !ok {
			log.Printf("[LiveModeManager] Argument de mode invalide: %v (type: %T)", args[0], args[0])
			return
		}

		log.Printf("[LiveModeManager] Changement de mode demandé vers: %s", targetMode)

		// Passer au mode demandé
		if err := m.SwitchMode(targetMode, SwitchModeOptions{}); err != nil {
			log.Printf("[LiveModeManager] Erreur lors du changement de mode vers %s: %v", targetMode, err)
		}
	})
}

// SetupGlobalHardwareListeners configure les écouteurs pour les événements globaux du hardware
func (m *LiveModeManager) SetupGlobalHardwareListeners(hardwareManager *communication.HardwareManager) {
	if hardwareManager == nil {
		log.Println("[LiveModeManager] HardwareManager non fourni pour les événements globaux")
		return
	}

	log.Println("[LiveModeManager] Configuration des écouteurs d'événements globaux...")

	// Écouter l'événement globalPlay (bouton B10)
	hardwareManager.On("globalPlay", func(args []interface{}) {
		log.Println("[LiveModeManager] Événement globalPlay reçu - Envoi OSC /live/song/start_playing")
		if err := m.Send("/live/song/start_playing"); err != nil {
			log.Printf("[LiveModeManager] Erreur envoi OSC globalPlay: %v", err)
		}
	})

	// Écouter l'événement globalStop (bouton B11)
	hardwareManager.On("globalStop", func(args []interface{}) {
		log.Println("[LiveModeManager] Événement globalStop reçu - Envoi OSC /live/song/stop_playing")
		if err := m.Send("/live/song/stop_playing"); err != nil {
			log.Printf("[LiveModeManager] Erreur envoi OSC globalStop: %v", err)
		}
	})

	// Écouter l'événement globalRecord (bouton B12)
	hardwareManager.On("globalRecord", func(args []interface{}) {
		log.Println("[LiveModeManager] Événement globalRecord reçu - Envoi OSC /live/song/record")
		if err := m.Send("/live/song/record", 1); err != nil {
			log.Printf("[LiveModeManager] Erreur envoi OSC globalRecord: %v", err)
		}
	})

	log.Println("[LiveModeManager] Écouteurs d'événements globaux configurés avec succès")
}

// setupQuickViewListeners configure les écouteurs pour Enter/Exit QuickView
func (m *LiveModeManager) setupQuickViewListeners() {
	log.Println("Configuration des écouteurs pour les requêtes QuickView...")

	// Écouteur pour entrer en QuickView
	m.emitter.On("requestEnterQuickView", func(_ []interface{}) {
		log.Println("[LiveModeManager] Événement requestEnterQuickView reçu")
		err := m.EnterTrackQuickView()
		if err != nil {
			log.Printf("[LiveModeManager] Erreur en entrant en QuickView: %v", err)
		}
	})

	// Écouteur pour sortir de QuickView
	m.emitter.On("requestExitQuickView", func(_ []interface{}) {
		log.Println("[LiveModeManager] Événement requestExitQuickView reçu")
		err := m.ExitTrackQuickView()
		if err != nil {
			log.Printf("[LiveModeManager] Erreur en sortant de QuickView: %v", err)
		}
	})
}

// RegisterMode enregistre un nouveau mode et l'initialise
func (m *LiveModeManager) RegisterMode(name string, mode communication.OscMode) {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.modes[name]; exists {
		log.Printf("AVERTISSEMENT: Tentative d'enregistrement d'un mode déjà existant: %s", name)
		return
	}

	// Initialiser le mode avec les services nécessaires
	mode.Initialize(m)
	m.modes[name] = mode

	// Ajouter un écouteur pour l'événement "modeChange" du mode
	if baseMode, ok := mode.(interface {
		GetEventEmitter() *communication.EventEmitter
	}); ok {
		eventEmitter := baseMode.GetEventEmitter()
		if eventEmitter != nil {
			eventEmitter.On("modeChange", func(args []interface{}) {
				if len(args) > 0 {
					if targetMode, ok := args[0].(string); ok {
						log.Printf("[LiveModeManager] Événement modeChange reçu du mode %s vers le mode %s", name, targetMode)
						m.SwitchMode(targetMode, SwitchModeOptions{})
					}
				}
			})
			log.Printf("Écouteur 'modeChange' ajouté pour le mode %s", name)
			
			// Ajouter les écouteurs QuickView pour chaque mode
			eventEmitter.On("requestEnterQuickView", func(_ []interface{}) {
				log.Printf("[LiveModeManager] Événement requestEnterQuickView reçu du mode %s", name)
				err := m.EnterTrackQuickView()
				if err != nil {
					log.Printf("[LiveModeManager] Erreur en entrant en QuickView depuis le mode %s: %v", name, err)
				}
			})
			
			eventEmitter.On("requestExitQuickView", func(_ []interface{}) {
				log.Printf("[LiveModeManager] Événement requestExitQuickView reçu du mode %s", name)
				err := m.ExitTrackQuickView()
				if err != nil {
					log.Printf("[LiveModeManager] Erreur en sortant de QuickView depuis le mode %s: %v", name, err)
				}
			})
			
			log.Printf("Écouteurs QuickView ajoutés pour le mode %s", name)
		}
	}

	log.Printf("Mode %s enregistré et initialisé", name)
}

// SwitchModeOptions contient les options pour le changement de mode
type SwitchModeOptions struct {
	QuickView bool
}

// SwitchMode change le mode actif
func (m *LiveModeManager) SwitchMode(newModeName string, options SwitchModeOptions) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	log.Printf("Tentative de passer au mode: %s (QuickView: %v)", newModeName, options.QuickView)

	// Vérifier si le nouveau mode existe
	newMode, exists := m.modes[newModeName]
	if !exists {
		return fmt.Errorf("mode inconnu: %s", newModeName)
	}

	// Si on est déjà dans ce mode et que les options QuickView sont les mêmes, ne rien faire
	if m.currentActiveModeName == newModeName {
		if newModeName == "track" {
			if m.isInQuickView == options.QuickView {
				log.Printf("Déjà dans le mode %s avec QuickView=%v", newModeName, options.QuickView)
				return nil
			}
		} else {
			return nil
		}
	}

	// Désactiver le mode actuel
	if m.currentActiveModeName != "" {
		if currentMode, ok := m.modes[m.currentActiveModeName]; ok {
			currentMode.Deactivate()
		}
	}

	// Activer le nouveau mode
	m.currentActiveModeName = newModeName

	// Si c'est le mode track et qu'on est en QuickView, l'initialiser en QuickView
	if newModeName == "track" {
		if trackMode, ok := newMode.(interface{ InitializeMode(bool) }); ok {
			trackMode.InitializeMode(options.QuickView)
		} else if trackMode, ok := newMode.(interface{ SetQuickViewState(bool) }); ok {
			// Si InitializeMode n'est pas disponible, essayer SetQuickViewState
			trackMode.SetQuickViewState(options.QuickView)
		}
	}

	// Libérer le verrou avant d'activer le nouveau mode pour éviter un deadlock
	m.mu.Unlock()

	// Activer le nouveau mode (sans verrou)
	newMode.Activate()

	// Reprendre le verrou pour terminer la fonction
	m.mu.Lock()

	return nil
}

// Cleanup nettoie tous les modes avant la sortie de l'application
func (m *LiveModeManager) Cleanup() {
	m.mu.Lock() // Prendre le verrou pour le nettoyage
	defer m.mu.Unlock()

	log.Println("Nettoyage de LiveModeManager...")

	// Nettoyer tous les modes
	for name, mode := range m.modes {
		log.Printf("Nettoyage complet du mode: %s", name)
		// On ne peut pas appeler CleanupForExit en détenant le verrou du manager
		// si CleanupForExit interagit avec le manager. Il faut le faire après.
		// Pour l'instant, on suppose que CleanupForExit est sûr ou ne rappelle pas le manager.
		// Solution plus sûre : collecter les modes, libérer, puis nettoyer.
		mode.CleanupForExit()
	}

	m.modes = make(map[string]communication.OscMode)
	m.emitter = communication.NewEventEmitter()
	m.currentActiveModeName = ""

	log.Println("LiveModeManager nettoyé avec succès")
}

// GetCurrentActiveModeName retourne le nom du mode actif
func (m *LiveModeManager) GetCurrentActiveModeName() string {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.currentActiveModeName
}

// GetMode retourne un mode par son nom
func (m *LiveModeManager) GetMode(name string) (communication.OscMode, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	mode, exists := m.modes[name]
	return mode, exists
}

// GetTrackManager retourne le gestionnaire de pistes
func (m *LiveModeManager) GetTrackManager() *LiveTrackManager {
	return m.trackManager
}

// Implémentation de l'interface OscService
func (m *LiveModeManager) On(address string, handler func([]interface{})) {
	m.emitter.On(address, handler)
}

func (m *LiveModeManager) Off(address string, handler func([]interface{})) {
	m.emitter.Off(address, handler)
}

func (m *LiveModeManager) Send(address string, args ...interface{}) error {
	if m.oscBridge == nil {
		return fmt.Errorf("le bridge OSC n'est pas initialisé")
	}
	return m.oscBridge.Send(address, args...)
}

// GetVolumeConverter retourne le convertisseur de volume
func (m *LiveModeManager) GetVolumeConverter() *utils.VolumeConverter {
	return m.volumeConverter
}

// GetVolumeSendConverter retourne le convertisseur de volume pour l'envoi
func (m *LiveModeManager) GetVolumeSendConverter() *utils.VolumeSendConverter {
	return m.volumeSendConverter
}

// GetLearnData retourne les données d'apprentissage
func (m *LiveModeManager) GetLearnData() *types.LearnData {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.learnData
}

// SetLearnData définit les données d'apprentissage
func (m *LiveModeManager) SetLearnData(data *types.LearnData) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.learnData = data
}

// InitializeAndStartMode initialise tous les modes et démarre avec le mode volume
func (m *LiveModeManager) InitializeAndStartMode() error {
	log.Println("Démarrage de l'initialisation des modes...")

	// Passer au mode par défaut au démarrage
	if err := m.SwitchMode("volume", SwitchModeOptions{}); err != nil {
		return fmt.Errorf("erreur lors du passage au mode initial 'track': %v", err)
	}

	log.Println("Mode initial 'track' activé avec succès")
	return nil
}

// EnterTrackQuickView active temporairement le mode Track en QuickView
func (m *LiveModeManager) EnterTrackQuickView() error {
	m.mu.Lock()

	log.Println("=== Début EnterTrackQuickView ===")

	if m.isInQuickView {
		log.Println("Déjà en QuickView (selon l'état du manager). Pour forcer, sortir puis ré-entrer.")
		m.mu.Unlock()
		// Si le manager pense déjà être en QuickView, on ne force pas un nouveau switch
		// pour éviter des boucles ou des états incohérents si Exit n'a pas été appelé.
		// L'utilisateur doit explicitement sortir de QuickView puis y ré-entrer.
		return nil
	}

	// Sauvegarder le mode actuel
	currentModeBeforeQuickView := m.currentActiveModeName
	m.previousModeName = currentModeBeforeQuickView

	// NE PAS mettre m.isInQuickView = true ici
	// NE PAS émettre quickViewStateChanged ici

	// Garder une copie des variables nécessaires et libérer le mutex
	m.mu.Unlock()

	// Passer en mode track avec quickView (sans mutex verrouillé)
	err := m.SwitchMode("track", SwitchModeOptions{QuickView: true})
	if err != nil {
		// En cas d'erreur, réinitialiser les variables
		m.mu.Lock()
		m.previousModeName = "" // Nettoyer car on n'est pas entré en QuickView
		// m.isInQuickView reste false, ce qui est correct
		m.mu.Unlock()
		log.Printf("[LiveModeManager] Erreur lors du passage en QuickView: %v", err)
		return fmt.Errorf("erreur lors du passage en QuickView: %v", err)
	}

	// Si SwitchMode a réussi:
	m.mu.Lock()
	m.isInQuickView = true // Mettre à jour l'état du manager MAINTENANT
	m.mu.Unlock()

	// Notifier tous les modes du changement de QuickView APRÈS que tout soit en place
	m.emitter.Emit("quickViewStateChanged", []interface{}{true})

	log.Printf("Mode Track activé en QuickView (mode précédent: %s)", m.previousModeName)
	log.Println("=== Fin EnterTrackQuickView ===")
	return nil
}

// ExitTrackQuickView quitte le mode Track QuickView et restaure le mode précédent
func (m *LiveModeManager) ExitTrackQuickView() error {
	m.mu.Lock()

	log.Println("=== Début ExitTrackQuickView ===")

	if !m.isInQuickView {
		log.Println("Pas en QuickView (selon l'état du manager)")
		m.mu.Unlock()
		return nil
	}

	// Garder une copie des variables nécessaires
	previousModeToRestore := m.previousModeName

	// NE PAS mettre m.isInQuickView = false ici
	// NE PAS émettre quickViewStateChanged ici
	// NE PAS vider m.previousModeName ici

	// Libérer le mutex avant d'appeler SwitchMode
	m.mu.Unlock()

	// Restaurer le mode précédent (sans mutex verrouillé)
	if previousModeToRestore != "" {
		// Toujours s'assurer que QuickView est explicitement false pour le mode restauré
		err := m.SwitchMode(previousModeToRestore, SwitchModeOptions{QuickView: false})
		if err != nil {
			// Le SwitchMode a échoué, on est toujours techniquement en QuickView
			// Ne pas changer m.isInQuickView ni m.previousModeName dans le manager pour refléter cet échec.
			log.Printf("[LiveModeManager] Erreur lors du retour au mode précédent: %v", err)
			return fmt.Errorf("erreur lors du retour au mode précédent: %v", err)
		}
		log.Printf("Retour au mode précédent: %s", previousModeToRestore)
	} else {
		// Si on était en QuickView mais pas de previousModeName, c'est un état inattendu.
		// On va quand même tenter de mettre à jour l'état interne du manager.
		log.Println("[LiveModeManager] AVERTISSEMENT: isInQuickView était true, mais previousModeName était vide.")
	}

	// Reprendre le mutex pour mettre à jour les variables finales du manager
	m.mu.Lock()
	m.isInQuickView = false
	m.previousModeName = ""
	m.mu.Unlock()

	// Notifier tous les modes de la sortie du QuickView APRÈS que tout soit en place
	m.emitter.Emit("quickViewStateChanged", []interface{}{false})

	log.Println("=== Fin ExitTrackQuickView ===")
	return nil
}

// IsInQuickView retourne si le gestionnaire est actuellement en QuickView
func (m *LiveModeManager) IsInQuickView() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.isInQuickView
}
