package communication

import (
	"log"
)

// ExampleMode montre comment utiliser ButtonMode avec l'envoi de messages OSC
type ExampleMode struct {
	*BaseMode
	buttonManager *ButtonManager
}

// NewExampleMode crée un nouveau mode d'exemple
func NewExampleMode() *ExampleMode {
	mode := &ExampleMode{
		BaseMode:      NewBaseMode(),
		buttonManager: NewButtonManager(),
	}
	
	return mode
}

// GetButtonManager implémente l'interface ButtonMode
func (m *ExampleMode) GetButtonManager() *ButtonManager {
	return m.buttonManager
}

// SetupCommonButtons configure les boutons communs pour ce mode
func (m *ExampleMode) SetupCommonButtons() {
	log.Println("Configuration des boutons communs pour ExampleMode")
	
	// Utiliser les actions par défaut qui envoient les messages OSC standards
	defaultActions := DefaultCommonButtonActions()
	
	// Configurer les handlers communs avec le BaseMode pour l'envoi OSC
	SetupCommonButtonHandlers(m.buttonManager, defaultActions, m.BaseMode)
	
	log.Println("Boutons communs configurés:")
	log.Println("  - B10: Play (/live/play)")
	log.Println("  - B11: Stop (/live/stop)")
	log.Println("  - B12: Record (/live/record)")
}

// SetupModeButtons configure les boutons spécifiques à ce mode
func (m *ExampleMode) SetupModeButtons() {
	log.Println("Configuration des boutons spécifiques pour ExampleMode")
	
	// Exemple de boutons spécifiques au mode
	m.buttonManager.RegisterModeHandler(0, func(buttonIndex int) bool {
		log.Printf("ExampleMode: Bouton spécifique %d appuyé", buttonIndex)
		// Envoyer un message OSC spécifique au mode
		err := m.BaseMode.Send("/live/example/action1")
		if err != nil {
			log.Printf("Erreur envoi OSC: %v", err)
		}
		return true
	})
	
	m.buttonManager.RegisterModeHandler(1, func(buttonIndex int) bool {
		log.Printf("ExampleMode: Bouton spécifique %d appuyé", buttonIndex)
		// Envoyer un message OSC avec paramètres
		err := m.BaseMode.Send("/live/example/action2", 42, "test")
		if err != nil {
			log.Printf("Erreur envoi OSC: %v", err)
		}
		return true
	})
	
	log.Println("Boutons spécifiques configurés:")
	log.Println("  - B00: Action1 (/live/example/action1)")
	log.Println("  - B01: Action2 (/live/example/action2)")
}

// Initialize initialise le mode et configure tous les boutons
func (m *ExampleMode) Initialize(service OscService) {
	log.Println("Initialisation d'ExampleMode...")
	
	// Initialiser le mode de base
	m.BaseMode.Initialize(service)
	
	// Configurer les boutons communs et spécifiques
	m.SetupCommonButtons()
	m.SetupModeButtons()
	
	log.Println("ExampleMode initialisé avec succès")
}

// Activate active le mode
func (m *ExampleMode) Activate() {
	log.Println("Activation d'ExampleMode")
	m.BaseMode.Activate()
	
	// Activer le gestionnaire de boutons si nécessaire
	// (dépend de l'implémentation du ButtonManager)
}

// Deactivate désactive le mode
func (m *ExampleMode) Deactivate() {
	log.Println("Désactivation d'ExampleMode")
	m.BaseMode.Deactivate()
	
	// Désactiver le gestionnaire de boutons si nécessaire
}

// CleanupForExit nettoie les ressources
func (m *ExampleMode) CleanupForExit() {
	log.Println("Nettoyage d'ExampleMode")
	m.BaseMode.CleanupForExit()
	
	// Nettoyer le gestionnaire de boutons si nécessaire
}

// CustomCommonButtonActionsExample montre comment créer des actions personnalisées
func CustomCommonButtonActionsExample() *CommonButtonActions {
	return &CommonButtonActions{
		Play: func(baseMode *BaseMode) bool {
			log.Println("Action Play personnalisée")
			if baseMode != nil {
				// Envoyer un message personnalisé au lieu du message standard
				err := baseMode.Send("/live/custom/play", "custom_parameter")
				if err != nil {
					log.Printf("Erreur envoi OSC personnalisé: %v", err)
				}
				return true
			}
			return false
		},
		Stop: func(baseMode *BaseMode) bool {
			log.Println("Action Stop personnalisée")
			if baseMode != nil {
				// Envoyer plusieurs messages OSC
				baseMode.Send("/live/custom/stop")
				baseMode.Send("/live/custom/reset")
				return true
			}
			return false
		},
		Record: func(baseMode *BaseMode) bool {
			log.Println("Action Record personnalisée")
			if baseMode != nil {
				// Envoyer un message avec des paramètres complexes
				err := baseMode.Send("/live/custom/record", map[string]interface{}{
					"mode": "overdub",
					"quantize": true,
				})
				if err != nil {
					log.Printf("Erreur envoi OSC personnalisé: %v", err)
				}
				return true
			}
			return false
		},
	}
}