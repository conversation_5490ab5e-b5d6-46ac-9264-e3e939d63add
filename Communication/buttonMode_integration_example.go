package communication

import (
	"log"
)

// ExampleModeWithButtons montre comment implémenter l'interface ButtonMode
type ExampleModeWithButtons struct {
	buttonManager *ButtonManager
	name          string
}

// NewExampleModeWithButtons crée un nouveau mode d'exemple
func NewExampleModeWithButtons(name string) *ExampleModeWithButtons {
	return &ExampleModeWithButtons{
		buttonManager: NewButtonManager(),
		name:          name,
	}
}

// GetButtonManager implémente l'interface ButtonMode
func (m *ExampleModeWithButtons) GetButtonManager() *ButtonManager {
	return m.buttonManager
}

// SetupCommonButtons implémente l'interface ButtonMode
func (m *ExampleModeWithButtons) SetupCommonButtons() {
	// Configurer les actions communes pour ce mode
	actions := &CommonButtonActions{
		Play: func() bool {
			log.Printf("[%s] Action Play commune exécutée", m.name)
			return true
		},
		Stop: func() bool {
			log.Printf("[%s] Action Stop commune exécutée", m.name)
			return true
		},
		Record: func() bool {
			log.Printf("[%s] Action Record commune exécutée", m.name)
			return true
		},
	}
	
	// Utiliser la fonction helper pour configurer les boutons communs
	SetupCommonButtonHandlers(m.buttonManager, actions)
}

// SetupModeButtons implémente l'interface ButtonMode
func (m *ExampleModeWithButtons) SetupModeButtons() {
	// Configurer les boutons spécifiques à ce mode
	m.buttonManager.RegisterModeHandler(1, func(buttonIndex int) bool {
		log.Printf("[%s] Bouton spécifique B01 appuyé", m.name)
		return true
	})
	
	m.buttonManager.RegisterModeHandler(2, func(buttonIndex int) bool {
		log.Printf("[%s] Bouton spécifique B02 appuyé", m.name)
		return true
	})
}

// ExampleIntegrationWithHardwareManager montre comment intégrer avec le HardwareManager
func ExampleIntegrationWithHardwareManager() {
	// 1. Créer le gestionnaire de communication
	comManager := NewCommunicationManager()
	
	// 2. Récupérer le HardwareManager
	hardwareManager := comManager.GetHardwareManager()
	
	// 3. Les boutons transversaux (B05, B10, B11, B12) sont automatiquement
	//    gérés par le HardwareManager avec une logique directe et simple
	
	// 4. S'abonner aux événements globaux si nécessaire
	ExampleButtonEventHandling(hardwareManager)
	
	// 5. Créer un mode qui implémente ButtonMode
	exampleMode := NewExampleModeWithButtons("ExampleMode")
	
	// 6. Configurer les boutons du mode
	exampleMode.SetupCommonButtons()
	exampleMode.SetupModeButtons()
	
	log.Println("Intégration avec HardwareManager configurée avec succès")
	log.Println("Les boutons transversaux B05, B10, B11, B12 sont gérés automatiquement")
}

// ExampleButtonEventHandling montre comment gérer les événements de boutons
func ExampleButtonEventHandling(hardwareManager *HardwareManager) {
	// S'abonner aux événements globaux émis par les boutons transversaux
	hardwareManager.On("globalPlay", func(args []interface{}) {
		log.Println("Événement globalPlay reçu - démarrer la lecture globale")
	})
	
	hardwareManager.On("globalStop", func(args []interface{}) {
		log.Println("Événement globalStop reçu - arrêter la lecture globale")
	})
	
	hardwareManager.On("globalRecord", func(args []interface{}) {
		log.Println("Événement globalRecord reçu - démarrer l'enregistrement global")
	})
}