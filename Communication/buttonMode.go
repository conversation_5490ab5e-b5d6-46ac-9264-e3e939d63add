package communication

// ButtonMode est une interface que les modes peuvent implémenter
// pour bénéficier de la gestion commune des boutons
type ButtonMode interface {
	// GetButtonManager retourne le gestionnaire de boutons du mode
	GetButtonManager() *ButtonManager
	
	// SetupCommonButtons configure les boutons communs pour ce mode
	SetupCommonButtons()
	
	// SetupModeButtons configure les boutons spécifiques à ce mode
	SetupModeButtons()
}

// CommonButtonActions définit les actions communes disponibles
type CommonButtonActions struct {
	Play   func() bool
	Stop   func() bool
	Record func() bool
}

// SetupCommonButtonHandlers configure les handlers communs standard
func SetupCommonButtonHandlers(bm *ButtonManager, actions *CommonButtonActions) {
	if actions.Play != nil {
		bm.RegisterCommonHandler(10, func(buttonIndex int) bool {
			return actions.Play()
		})
	}
	
	if actions.Stop != nil {
		bm.RegisterCommonHandler(11, func(buttonIndex int) bool {
			return actions.Stop()
		})
	}
	
	if actions.Record != nil {
		bm.RegisterCommonHandler(12, func(buttonIndex int) bool {
			return actions.Record()
		})
	}
}
