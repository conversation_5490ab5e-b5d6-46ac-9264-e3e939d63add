package communication

import (
	"fmt"
	"log"
	"strconv"
	"strings"
)

// TouchEvent représente un événement tactile
type TouchEvent struct {
	TouchType string // Type de toucher (deux lettres)
	Index     int    // Index du capteur tactile
	Index2    int    // Second index pour les événements qui en nécessitent deux (comme mv)
}

// ButtonEvent représente un événement de bouton
type ButtonEvent struct {
	Index int // Index du bouton
	State int // État du bouton (0=relâché, 1=appuyé)
}

// EncoderEvent représente un événement d'encodeur
type EncoderEvent struct {
	Index int // Index de l'encodeur
	Value int // Valeur de l'encodeur (peut être positif ou négatif)
}

// HardwareEvent représente un événement matériel typé
type HardwareEvent struct {
	Type         string        // "touch", "button", "encoder"
	TouchEvent   *TouchEvent   // Présent si Type == "touch"
	ButtonEvent  *ButtonEvent  // Présent si Type == "button"
	EncoderEvent *EncoderEvent // Présent si Type == "encoder"
	RawMessage   string        // Message brut original
}

// HardwareEventHandler est le type pour les fonctions de gestion d'événements matériels
type HardwareEventHandler func(event HardwareEvent)

// HardwareManager gère les événements matériels provenant de l'ESP32
type HardwareManager struct {
	*EventEmitter
	communicationManager *CommunicationManager
	activeMode           HardwareEventReceiver
	modeManager          ModeManagerInterface // Interface pour communiquer avec le gestionnaire de modes
	isInitialized        bool
	debug                bool // Activer les logs de debug
}

// ModeManagerInterface définit les méthodes nécessaires pour communiquer avec le gestionnaire de modes
type ModeManagerInterface interface {
	EnterTrackQuickView() error
	ExitTrackQuickView() error
}

// HardwareEventReceiver est une interface que les modes peuvent implémenter
// pour recevoir des événements matériels
type HardwareEventReceiver interface {
	HandleHardwareEvent(event HardwareEvent)
}

// NewHardwareManager crée une nouvelle instance de HardwareManager
func NewHardwareManager(comManager *CommunicationManager) *HardwareManager {
	return &HardwareManager{
		EventEmitter:         NewEventEmitter(),
		communicationManager: comManager,
		modeManager:          nil, // Sera défini plus tard
		isInitialized:        false,
		debug:                true, // Activer les logs de debug par défaut
	}
}

// SetModeManager définit le gestionnaire de modes pour les boutons transversaux
func (hm *HardwareManager) SetModeManager(modeManager ModeManagerInterface) {
	hm.modeManager = modeManager
	if hm.debug {
		log.Printf("[HW] Mode manager défini pour les boutons transversaux")
	}
}

// Initialize initialise le gestionnaire matériel
func (hm *HardwareManager) Initialize() error {
	if hm.isInitialized {
		return nil
	}

	if hm.communicationManager == nil {
		return fmt.Errorf("le gestionnaire de communication n'est pas défini")
	}

	// S'abonner aux messages du gestionnaire de communication
	hm.communicationManager.On("message", func(args []interface{}) {
		if len(args) > 0 {
			if message, ok := args[0].(string); ok {
				if hm.debug {
					log.Printf("[HW] Message reçu pour traitement matériel: %s", message)
				}
				hm.handleHardwareMessage(message)
			}
		}
	})

	hm.isInitialized = true
	log.Println("Gestionnaire matériel initialisé avec succès")
	return nil
}

// SetActiveMode définit le mode actif qui recevra les événements matériels
func (hm *HardwareManager) SetActiveMode(mode HardwareEventReceiver) {
	hm.activeMode = mode
	log.Printf("Mode actif défini pour les événements matériels: %T", mode)
}

// SetDebug active ou désactive les logs de debug
func (hm *HardwareManager) SetDebug(debug bool) {
	hm.debug = debug
}

// handleHardwareMessage analyse et traite les messages matériels entrants
func (hm *HardwareManager) handleHardwareMessage(message string) {
	parts := strings.Split(message, ",")
	if len(parts) < 2 {
		// Ce n'est probablement pas un message hardware, ignorer
		if hm.debug {
			log.Printf("[HW] Message ignoré (format incorrect): %s", message)
		}
		return
	}

	// Premier caractère du message indique le type d'événement
	eventType := parts[0]
	var event HardwareEvent

	if hm.debug {
		log.Printf("[HW] Analyse du message type=%s: %s", eventType, message)
	}

	switch eventType {
	case "t": // Touch event
		if len(parts) < 3 {
			log.Printf("[HW] Format de message tactile invalide: %s", message)
			return
		}

		touchType := parts[1] // Type de toucher (deux lettres)
		indexStr := parts[2]  // Index du capteur

		index, err := strconv.Atoi(indexStr)
		if err != nil {
			log.Printf("[HW] Index tactile invalide: %s", indexStr)
			return
		}

		// Initialiser index2 à 0 par défaut
		index2 := 0
		// Si un deuxième index est fourni (comme pour mv), le lire
		if len(parts) > 3 && touchType == "mv" {
			if i2, err := strconv.Atoi(parts[3]); err == nil {
				index2 = i2
			}
		}

		event = HardwareEvent{
			Type: "touch",
			TouchEvent: &TouchEvent{
				TouchType: touchType,
				Index:     index,
				Index2:    index2,
			},
			RawMessage: message,
		}

		if hm.debug {
			if touchType == "mv" {
				log.Printf("[HW] Événement tactile créé: type=%s, index=%d, index2=%d", touchType, index, index2)
			} else {
				log.Printf("[HW] Événement tactile créé: type=%s, index=%d", touchType, index)
			}
		}

	case "b": // Button event
		if len(parts) < 3 {
			log.Printf("[HW] Format de message bouton invalide: %s", message)
			return
		}

		indexStr := parts[1] // Index du bouton
		stateStr := parts[2] // État du bouton (0=relâché, 1=appuyé)

		index, err := strconv.Atoi(indexStr)
		if err != nil {
			log.Printf("[HW] Index bouton invalide: %s", indexStr)
			return
		}

		state, err := strconv.Atoi(stateStr)
		if err != nil {
			log.Printf("[HW] État bouton invalide: %s", stateStr)
			return
		}

		event = HardwareEvent{
			Type: "button",
			ButtonEvent: &ButtonEvent{
				Index: index,
				State: state,
			},
			RawMessage: message,
		}

		if hm.debug {
			log.Printf("[HW] Événement bouton créé: index=%d, state=%d", index, state)
		}

	case "e": // Encoder event
		if len(parts) < 3 {
			log.Printf("[HW] Format de message encodeur invalide: %s", message)
			return
		}

		indexStr := parts[1] // Index de l'encodeur
		valueStr := parts[2] // Valeur de l'encodeur

		index, err := strconv.Atoi(indexStr)
		if err != nil {
			log.Printf("[HW] Index encodeur invalide: %s", indexStr)
			return
		}

		value, err := strconv.Atoi(valueStr)
		if err != nil {
			log.Printf("[HW] Valeur encodeur invalide: %s", valueStr)
			return
		}

		event = HardwareEvent{
			Type: "encoder",
			EncoderEvent: &EncoderEvent{
				Index: index,
				Value: value,
			},
			RawMessage: message,
		}

		if hm.debug {
			log.Printf("[HW] Événement encodeur créé: index=%d, valeur=%d", index, value)
		}

	default:
		// Ce n'est pas un message hardware reconnu, ignorer
		if hm.debug {
			log.Printf("[HW] Type de message non reconnu: %s", eventType)
		}
		return
	}

	// Gérer les boutons transversaux AVANT d'envoyer au mode actif
	if event.Type == "button" && event.ButtonEvent != nil && event.ButtonEvent.State == 1 {
		handled := hm.handleTransversalButton(event.ButtonEvent.Index)
		if handled {
			if hm.debug {
				log.Printf("[HW] Bouton transversal B%02d géré, événement non transmis au mode", event.ButtonEvent.Index)
			}
			return // Ne pas transmettre au mode actif
		}
	}
	

	// Émettre l'événement pour les abonnés externes
	hm.Emit(event.Type, []interface{}{event})
	if hm.debug {
		log.Printf("[HW] Événement '%s' émis", event.Type)
	}

	// Émettre un événement général "hardware" pour tous les types
	hm.Emit("hardware", []interface{}{event})

	// Envoyer l'événement au mode actif si défini
	if hm.activeMode != nil {
		if hm.debug {
			log.Printf("[HW] Transmission de l'événement au mode actif: %T", hm.activeMode)
		}
		hm.activeMode.HandleHardwareEvent(event)
	} else {
		if hm.debug {
			log.Printf("[HW] Aucun mode actif défini pour recevoir l'événement")
		}
	}
}

// handleTransversalButton gère les boutons transversaux (communs à tous les modes)
// Retourne true si le bouton a été géré et ne doit pas être transmis au mode actif
func (hm *HardwareManager) handleTransversalButton(buttonIndex int) bool {
	switch buttonIndex {
	case 5: // B05 - QuickView
		if hm.debug {
			log.Printf("[HW] Bouton transversal B05 - Demande d'entrée en QuickView")
		}
		if hm.modeManager != nil {
			err := hm.modeManager.EnterTrackQuickView()
			if err != nil {
				log.Printf("[HW] Erreur en entrant en QuickView: %v", err)
			}
		} else {
			log.Printf("[HW] Mode manager non défini, impossible d'entrer en QuickView")
		}
		return true

	case 10: // B10 - Play global
		if hm.debug {
			log.Printf("[HW] Bouton transversal B10 - Play global")
		}
		hm.Emit("globalPlay", []interface{}{})
		return true

	case 11: // B11 - Stop global
		if hm.debug {
			log.Printf("[HW] Bouton transversal B11 - Stop global")
		}
		hm.Emit("globalStop", []interface{}{})
		return true

	case 12: // B12 - Record global
		if hm.debug {
			log.Printf("[HW] Bouton transversal B12 - Record global")
		}
		hm.Emit("globalRecord", []interface{}{})
		return true

	default:
		return false // Bouton non transversal, laisser passer au mode actif
	}
}


